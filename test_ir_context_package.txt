# USER QUERY
How does the context request system work?

# INTELLIGENT CONTEXT ANALYSIS
## Task: general_analysis
## Focus: Understand the context request implementation and architecture

## CRITICAL ENTITIES (2 most important)

### 1. process_context_request (method)
- File: aider_context_request_integration.py
- **Belongs to Class**: `AiderContextRequestIntegration`
- **Inherits From**: No inheritance (base class)
- Criticality: medium | Risk: medium

#### 🔁 Class Context
- Part of `AiderContextRequestIntegration` class

#### 🧩 Method Details
- **Calls super()**: No
- **Calls**: ["process_context_request", "get", "render_augmented_prompt"] (total: 3)
- **Used by**: ["base_coder_old", "base_coder", "aider_context_request_integration", "test_context_request_class_extraction"] (total: 4)
- **Side Effects**: writes_log, network_io, modifies_state

### 2. ContextRequestHandler (class)
- File: context_request_handler.py

- **Inherits From**: No inheritance (base class)
- Criticality: low | Risk: medium
- **Calls**: []
- **Used by**: ["test_ir_context_request", "base_coder", "aider_context_request_integration", "test_context_request_class_extraction"] (total: 4)
- **Side Effects**: none

## KEY IMPLEMENTATIONS (2 functions)
Complete code available on request for any function.

### 1. process_context_request
```python
    def process_context_request(self,
                               context_request: ContextRequest,
                               original_user_query: str,
                               repo_overview: str) -> str:
        """
        Process a context request and generate an augmented prompt.

        Args:
            context_request: The context request to process
            original_user_query: The original user query
            repo_overview: The repository overview

        Returns:
            An augmented prompt with the extracted context
        """
        # Log the inputs
        print("\n\n=== CONTEXT REQUEST PROCESSING ===")
        print(f"Original user query: {original_user_query}")
        print(f"Context request: {context_request}")
        print(f"Repo overview length: {len(repo_overview)} characters")
        print(f"Conversation history: {self.conversation_history}")

        # Increment the iteration counter
        self.current_iteration += 1

        # Process the context request
        extracted_context = self.context_handler.process_context_request(context_request)

        # Log the extracted context
        print("\n=== EXTRACTED CONTEXT ===")
        print(f"Original user query context: {extracted_context.get('original_user_query_context', '')}")
        print(f"Reason for request: {extracted_context.get('reason_for_request', '')}")
        print(f"Number of extracted symbols: {len(extracted_context.get('extracted_symbols', []))}")
    # ... (implementation continues)
```

### 2. ContextRequestHandler
```python
class ContextRequestHandler:
    """
    Handles context requests from the LLM, extracting the requested symbols
    and their dependencies using the surgical extraction system.
    """

```

## ANALYSIS INSTRUCTIONS
Based on the 2 critical entities above:

1. **Focus on HIGH criticality components** - these are the most important
2. **Consider change risk** - high risk = be careful with modifications
3. **Understand dependencies** - see what calls what
4. **Note side effects** - potential impacts of changes

**Your task**: How does the context request system work?

Provide specific, actionable insights based on this focused context.
